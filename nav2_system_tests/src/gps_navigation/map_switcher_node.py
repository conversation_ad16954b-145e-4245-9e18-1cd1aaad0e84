#!/usr/bin/env python3
# Copyright (c) 2023 Your Organization
# Licensed under the Apache License, Version 2.0

"""
地图切换节点，根据环境检测节点的信息自动切换地图和定位组件。

功能：
1. 监听环境信息变化
2. 切换到室内环境时：
   - 暂停导航
   - 载入对应的室内地图
   - 停用robot_localization_wrapper
   - 清理tf变换缓存
   - 激活amcl和indoor_ekf_wrapper
   - 运行tf监控
   - 恢复导航
3. 切换到室外环境时：
   - 暂停导航
   - 载入室外空白地图
   - 停用amcl和indoor_ekf_wrapper
   - 清理tf变换缓存
   - 激活robot_localization_wrapper
   - 运行tf监控
   - 恢复导航
"""

import rclpy
from rclpy.node import Node
from nav2_system_tests.msg import EnvInfo
from nav2_msgs.srv import LoadMap, ManageLifecycleNodes
from std_srvs.srv import Trigger
from geometry_msgs.msg import PoseWithCovarianceStamped, Pose, Quaternion
from sensor_msgs.msg import NavSatFix
from nav_msgs.msg import Odometry
import subprocess
import os
import time
import threading
import queue
import math
from enum import Enum


class SwitchingState(Enum):
    """地图切换状态枚举"""
    IDLE = "idle"
    SWITCHING_TO_INDOOR = "switching_to_indoor"
    SWITCHING_TO_OUTDOOR = "switching_to_outdoor"


class MapSwitcherNode(Node):
    """
    地图切换节点，根据环境变化自动切换地图和定位组件。
    """

    def __init__(self):
        """初始化地图切换节点。"""
        super().__init__('map_switcher')

        # 声明参数
        self.declare_parameter('outdoor_map_path', '/opt/overlay_ws/src/navigation2/nav2_system_tests/maps/blank_map_1000x1000.yaml')
        self.declare_parameter('indoor_areas_config', '')
        self.declare_parameter('tf_monitor_script', '')

        # 获取参数
        self.outdoor_map_path = self.get_parameter('outdoor_map_path').value
        self.indoor_areas_config_path = self.get_parameter('indoor_areas_config').value
        self.tf_monitor_script = self.get_parameter('tf_monitor_script').value

        # 加载室内区域配置
        self.indoor_areas_config = self.load_indoor_areas_config()

        # 状态变量
        self.current_environment = 'unknown'
        self.current_area_id = ''
        self.current_entrance_id = ''
        self.switching_state = SwitchingState.IDLE
        self.switching_lock = threading.Lock()

        # 线程管理 - 使用队列简化线程同步
        self.switch_queue = queue.Queue(maxsize=1)  # 只保留最新的切换请求
        self.switch_thread = None

        # 创建订阅者
        self.env_info_sub = self.create_subscription(
            EnvInfo,
            'environment_info',
            self.environment_info_callback,
            10)

        # 创建服务客户端
        self.map_loader_client = self.create_client(LoadMap, '/map_server/load_map')
        self.nav_manager_client = self.create_client(ManageLifecycleNodes, '/lifecycle_manager_navigation/manage_nodes')
        self.amcl_manager_client = self.create_client(ManageLifecycleNodes, '/lifecycle_manager_amcl/manage_nodes')
        self.indoor_ekf_manager_client = self.create_client(ManageLifecycleNodes, '/lifecycle_manager_indoor_ekf/manage_nodes')
        self.robot_loc_manager_client = self.create_client(ManageLifecycleNodes, '/lifecycle_manager_robot_localization/manage_nodes')

        # 创建初始位姿发布器
        self.initialpose_pub = self.create_publisher(
            PoseWithCovarianceStamped,
            '/initialpose',
            10)

        # 创建GPS和里程计订阅者
        self.gps_sub = self.create_subscription(
            NavSatFix,
            'gps/fix',
            self.gps_callback,
            10)

        self.global_odom_sub = self.create_subscription(
            Odometry,
            'odometry/global',
            self.global_odom_callback,
            10)

        # GPS和位姿状态变量
        self.last_gps_msg = None
        self.last_global_odom_msg = None
        self.gps_available = False

        self.get_logger().info('地图切换节点已初始化')
        self.get_logger().info(f'室外地图路径: {self.outdoor_map_path}')
        self.get_logger().info(f'室内区域配置: {self.indoor_areas_config_path}')
        self.get_logger().info(f'TF监控脚本: {self.tf_monitor_script}')

    def gps_callback(self, msg):
        """处理GPS消息回调。"""
        self.last_gps_msg = msg

        # 检查GPS是否有效
        if not math.isnan(msg.latitude) and not math.isnan(msg.longitude):
            self.gps_available = True
        else:
            self.gps_available = False
            self.get_logger().debug('收到无效GPS数据 (NaN值)')

    def global_odom_callback(self, msg):
        """处理全局里程计消息回调。"""
        self.last_global_odom_msg = msg



    def load_indoor_areas_config(self):
        """加载室内区域配置文件。"""
        import yaml

        if not self.indoor_areas_config_path or not os.path.exists(self.indoor_areas_config_path):
            self.get_logger().error(f'室内区域配置文件不存在: {self.indoor_areas_config_path}')
            return {}

        try:
            with open(self.indoor_areas_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            self.get_logger().info(f'成功加载室内区域配置，包含 {len(config.get("indoor_areas", []))} 个区域')
            return config
        except Exception as e:
            self.get_logger().error(f'加载室内区域配置失败: {str(e)}')
            return {}

    def get_indoor_map_path(self, area_id, entrance_id):
        """根据区域ID和入口ID获取室内地图路径。"""
        if not self.indoor_areas_config:
            return None

        # 查找对应的区域和入口
        for area in self.indoor_areas_config.get('indoor_areas', []):
            if area.get('id') == area_id:
                for entrance in area.get('entrances', []):
                    if entrance.get('id') == entrance_id:
                        map_file = entrance.get('map_file')
                        if map_file and os.path.exists(map_file):
                            return map_file
                        else:
                            self.get_logger().warning(f'地图文件不存在: {map_file}')
                            return None

        self.get_logger().warning(f'未找到区域 {area_id} 入口 {entrance_id} 的地图配置')
        return None

    def get_entrance_initial_pose(self, area_id, entrance_id):
        """根据区域ID和入口ID获取AMCL初始化位姿。"""
        if not self.indoor_areas_config:
            return None

        # 查找对应的区域和入口
        for area in self.indoor_areas_config.get('indoor_areas', []):
            if area.get('id') == area_id:
                for entrance in area.get('entrances', []):
                    if entrance.get('id') == entrance_id:
                        # 获取内侧点的坐标（机器人穿越内门线时的位置）
                        inner_point = entrance.get('points', {}).get('inner', {}).get('global_pose', {})
                        outer_point = entrance.get('points', {}).get('outer', {}).get('global_pose', {})

                        if inner_point:
                            # 使用内门线点作为初始化位置（机器人切换到室内时就在这个位置）
                            init_x = inner_point.get('x', 0.0)
                            init_y = inner_point.get('y', 0.0)

                            # 计算朝向：从外侧点指向内侧点的方向（机器人进入室内的朝向）
                            if outer_point:
                                dx = inner_point.get('x', 0.0) - outer_point.get('x', 0.0)
                                dy = inner_point.get('y', 0.0) - outer_point.get('y', 0.0)
                                init_yaw = math.atan2(dy, dx)
                            else:
                                # 如果没有外侧点信息，使用内侧点的朝向
                                init_yaw = inner_point.get('yaw', 0.0)

                            self.get_logger().info(f'使用内门线位置作为初始位姿: x={init_x:.3f}, y={init_y:.3f}, yaw={init_yaw:.3f}')
                            return init_x, init_y, init_yaw
                        else:
                            self.get_logger().warning(f'入口 {entrance_id} 缺少内门线位置信息')
                            return None

        self.get_logger().warning(f'未找到区域 {area_id} 入口 {entrance_id} 的位置配置')
        return None

    def publish_initial_pose(self, x, y, yaw):
        """发布AMCL初始位姿。"""
        try:
            # 创建初始位姿消息
            initial_pose = PoseWithCovarianceStamped()
            initial_pose.header.stamp = self.get_clock().now().to_msg()
            initial_pose.header.frame_id = 'map'

            # 设置位置
            initial_pose.pose.pose.position.x = x
            initial_pose.pose.pose.position.y = y
            initial_pose.pose.pose.position.z = 0.0

            # 设置朝向（从yaw角度转换为四元数）
            initial_pose.pose.pose.orientation.x = 0.0
            initial_pose.pose.pose.orientation.y = 0.0
            initial_pose.pose.pose.orientation.z = math.sin(yaw / 2.0)
            initial_pose.pose.pose.orientation.w = math.cos(yaw / 2.0)

            # 设置协方差矩阵（表示初始位姿的不确定性）
            # 对角线元素：x, y, z, roll, pitch, yaw的方差
            covariance = [0.0] * 36  # 6x6矩阵
            covariance[0] = 0.25   # x方差 (0.5m标准差)
            covariance[7] = 0.25   # y方差 (0.5m标准差)
            covariance[35] = 0.068 # yaw方差 (15度标准差)
            initial_pose.pose.covariance = covariance

            # 发布初始位姿
            self.initialpose_pub.publish(initial_pose)

            self.get_logger().info(f'已发布AMCL初始位姿: x={x:.3f}, y={y:.3f}, yaw={yaw:.3f}')
            return True

        except Exception as e:
            self.get_logger().error(f'发布初始位姿失败: {str(e)}')
            return False

    def auto_initialize_amcl(self, area_id, entrance_id):
        """自动初始化AMCL位姿，使用实时全局里程计位置。"""
        try:
            # 等待一段时间确保AMCL完全激活
            self.get_logger().info('等待AMCL完全激活...')
            time.sleep(3.0)

            # 优先使用实时全局里程计位置（已经由navsat_transform转换过的GPS坐标）
            if self.last_global_odom_msg:
                self.get_logger().info('使用实时全局里程计位置进行AMCL初始化')

                # 直接从全局里程计获取位置和朝向（已经是转换后的坐标）
                pose = self.last_global_odom_msg.pose.pose
                x = pose.position.x
                y = pose.position.y
                yaw = self.quaternion_to_yaw(pose.orientation)

                self.get_logger().info(f'全局里程计位置: x={x:.3f}, y={y:.3f}, yaw={yaw:.3f}')

            else:
                # 备选方案：使用配置文件中的位置
                self.get_logger().warning('全局里程计不可用，使用配置文件中的位置进行初始化')
                pose_info = self.get_entrance_initial_pose(area_id, entrance_id)
                if not pose_info:
                    self.get_logger().error(f'无法获取区域 {area_id} 入口 {entrance_id} 的初始位姿')
                    return False
                x, y, yaw = pose_info

            # 发布初始位姿
            success = self.publish_initial_pose(x, y, yaw)
            if success:
                self.get_logger().info(f'AMCL自动初始化成功: 区域={area_id}, 入口={entrance_id}')
                # 再等待一段时间让AMCL处理初始位姿
                time.sleep(2.0)
                return True
            else:
                self.get_logger().error('AMCL自动初始化失败')
                return False

        except Exception as e:
            self.get_logger().error(f'AMCL自动初始化异常: {str(e)}')
            return False

    def quaternion_to_yaw(self, quaternion):
        """将四元数转换为yaw角。"""
        # 计算yaw角
        siny_cosp = 2 * (quaternion.w * quaternion.z + quaternion.x * quaternion.y)
        cosy_cosp = 1 - 2 * (quaternion.y * quaternion.y + quaternion.z * quaternion.z)
        yaw = math.atan2(siny_cosp, cosy_cosp)
        return yaw

    def environment_info_callback(self, msg):
        """处理环境信息回调。"""
        # 只处理环境变化的消息
        if not msg.changed:
            return

        self.get_logger().info(f'收到环境变化信息: {msg.environment}, 区域: {msg.area_id}, 入口: {msg.entrance_id}')

        # 根据环境检测节点的状态机，判断实际的环境类型
        # 环境检测节点的状态包括：unknown, outdoor, entering_indoor, indoor, exiting_indoor
        actual_environment = self.determine_actual_environment(msg.environment)

        # 检查是否需要切换
        if actual_environment == self.current_environment and msg.area_id == self.current_area_id:
            self.get_logger().info('环境未发生实际变化，忽略')
            return

        # 更新当前环境信息
        old_environment = self.current_environment
        self.current_environment = actual_environment
        self.current_area_id = msg.area_id
        self.current_entrance_id = msg.entrance_id

        # 使用单一工作线程处理环境切换，避免创建多个线程
        self.schedule_environment_switch(old_environment, actual_environment, msg.area_id, msg.entrance_id)

    def determine_actual_environment(self, env_state):
        """根据环境检测节点的状态确定实际环境类型。"""
        # 环境检测节点的状态映射到实际环境
        if env_state in ['outdoor', 'unknown']:
            return 'outdoor'
        elif env_state in ['indoor']:
            return 'indoor'
        elif env_state in ['entering_indoor', 'exiting_indoor']:
            # 过渡状态，保持当前环境不变
            return self.current_environment
        else:
            self.get_logger().warning(f'未知环境状态: {env_state}，默认为室外')
            return 'outdoor'

    def schedule_environment_switch(self, old_env, new_env, area_id, entrance_id):
        """调度环境切换，使用队列和单一工作线程。"""
        # 清空队列并放入最新请求（非阻塞）
        try:
            # 清空旧请求
            while not self.switch_queue.empty():
                try:
                    self.switch_queue.get_nowait()
                except:
                    break

            # 放入新请求
            self.switch_queue.put_nowait((old_env, new_env, area_id, entrance_id))

            # 如果没有工作线程在运行，创建一个
            if self.switch_thread is None or not self.switch_thread.is_alive():
                self.switch_thread = threading.Thread(
                    target=self.switch_worker_thread,
                    daemon=True
                )
                self.switch_thread.start()
                self.get_logger().info('启动环境切换工作线程')

        except Exception as e:
            self.get_logger().error(f'调度环境切换失败: {str(e)}')

    def switch_worker_thread(self):
        """环境切换工作线程，处理队列中的切换请求。"""
        while True:
            try:
                # 等待切换请求，超时后退出
                old_env, new_env, area_id, entrance_id = self.switch_queue.get(timeout=1.0)

                # 执行环境切换
                self.handle_environment_switch(old_env, new_env, area_id, entrance_id)

                # 标记任务完成
                self.switch_queue.task_done()

            except queue.Empty:
                # 超时无新请求，退出线程
                break
            except Exception as e:
                self.get_logger().error(f'环境切换工作线程异常: {str(e)}')

        self.get_logger().info('环境切换工作线程退出')

    def handle_environment_switch(self, old_env, new_env, area_id, entrance_id):
        """处理环境切换逻辑。"""
        with self.switching_lock:
            if self.switching_state != SwitchingState.IDLE:
                self.get_logger().warning(f'正在进行环境切换 ({self.switching_state.value})，忽略新的切换请求')
                return

            try:
                if new_env == 'indoor':
                    self.switching_state = SwitchingState.SWITCHING_TO_INDOOR
                    self.get_logger().info(f'开始切换到室内环境 (区域: {area_id}, 入口: {entrance_id})')
                    self.switch_to_indoor(area_id, entrance_id)
                elif new_env == 'outdoor':
                    self.switching_state = SwitchingState.SWITCHING_TO_OUTDOOR
                    self.get_logger().info('开始切换到室外环境')
                    self.switch_to_outdoor()
                else:
                    self.get_logger().warning(f'未知环境类型: {new_env}')
                    return

                self.get_logger().info(f'环境切换完成: {old_env} -> {new_env}')

            except Exception as e:
                self.get_logger().error(f'环境切换失败: {str(e)}')
            finally:
                self.switching_state = SwitchingState.IDLE

    def switch_to_indoor(self, area_id, entrance_id):
        """切换到室内环境。"""
        self.get_logger().info('=== 开始切换到室内环境 ===')

        # 1. 暂停导航
        self.get_logger().info('1. 暂停导航系统...')
        self.pause_navigation()

        # 2. 载入室内地图
        self.get_logger().info(f'2. 载入室内地图 (区域: {area_id}, 入口: {entrance_id})...')
        indoor_map_path = self.get_indoor_map_path(area_id, entrance_id)
        if not indoor_map_path:
            raise FileNotFoundError(f'未找到区域 {area_id} 入口 {entrance_id} 的地图文件')
        self.load_map(indoor_map_path)

        # 3. 停用robot_localization_wrapper
        self.get_logger().info('3. 停用robot_localization_wrapper...')
        self.deactivate_component('robot_localization', self.robot_loc_manager_client)

        # 4. 清理tf变换缓存
        self.get_logger().info('4. 清理tf变换缓存...')
        self.clean_tf_cache()

        # 5. 激活amcl和indoor_ekf_wrapper
        self.get_logger().info('5. 激活AMCL和室内EKF...')
        self.activate_component('amcl', self.amcl_manager_client)
        self.activate_component('indoor_ekf', self.indoor_ekf_manager_client)

        # 6. 运行tf监控
        self.get_logger().info('6. 运行TF监控...')
        self.run_tf_monitor()

        # 7. 自动初始化AMCL
        self.get_logger().info('7. 自动初始化AMCL...')
        self.auto_initialize_amcl(area_id, entrance_id)

        # 8. 恢复导航
        self.get_logger().info('8. 恢复导航系统...')
        self.resume_navigation()

        self.get_logger().info('=== 室内环境切换完成 ===')

    def switch_to_outdoor(self):
        """切换到室外环境。"""
        self.get_logger().info('=== 开始切换到室外环境 ===')

        # 1. 暂停导航
        self.get_logger().info('1. 暂停导航系统...')
        self.pause_navigation()

        # 2. 载入室外空白地图
        self.get_logger().info('2. 载入室外空白地图...')
        self.load_map(self.outdoor_map_path)

        # 3. 停用amcl和indoor_ekf_wrapper
        self.get_logger().info('3. 停用AMCL和室内EKF...')
        self.deactivate_component('amcl', self.amcl_manager_client)
        self.deactivate_component('indoor_ekf', self.indoor_ekf_manager_client)

        # 4. 清理tf变换缓存
        self.get_logger().info('4. 清理tf变换缓存...')
        self.clean_tf_cache()

        # 5. 激活robot_localization_wrapper
        self.get_logger().info('5. 激活robot_localization_wrapper...')
        self.activate_component('robot_localization', self.robot_loc_manager_client)

        # 6. 运行tf监控
        self.get_logger().info('6. 运行TF监控...')
        self.run_tf_monitor()

        # 7. 恢复导航
        self.get_logger().info('7. 恢复导航系统...')
        self.resume_navigation()

        self.get_logger().info('=== 室外环境切换完成 ===')

    def load_map(self, map_path):
        """载入地图。"""
        if not os.path.exists(map_path):
            raise FileNotFoundError(f'地图文件不存在: {map_path}')

        if not self.map_loader_client.wait_for_service(timeout_sec=5.0):
            raise RuntimeError('地图载入服务不可用')

        request = LoadMap.Request()
        request.map_url = map_path

        future = self.map_loader_client.call_async(request)
        rclpy.spin_until_future_complete(self, future, timeout_sec=10.0)

        if future.result() is None:
            raise RuntimeError('地图载入服务调用失败')

        response = future.result()
        if response.result != LoadMap.Response.RESULT_SUCCESS:
            raise RuntimeError(f'地图载入失败，错误码: {response.result}')

        self.get_logger().info(f'地图载入成功: {map_path}')

    def pause_navigation(self):
        """暂停导航系统。"""
        # 使用常量值而不是Request类的属性
        self.call_lifecycle_service(self.nav_manager_client, 1, '导航系统暂停')  # PAUSE = 1

    def resume_navigation(self):
        """恢复导航系统。"""
        # 使用常量值而不是Request类的属性
        self.call_lifecycle_service(self.nav_manager_client, 2, '导航系统恢复')  # RESUME = 2

    def activate_component(self, component_name, client):
        """激活组件。"""
        # 使用常量值而不是Request类的属性
        self.call_lifecycle_service(client, 0, f'{component_name}激活')  # STARTUP = 0

    def deactivate_component(self, component_name, client):
        """停用组件。"""
        # 使用常量值而不是Request类的属性
        self.call_lifecycle_service(client, 1, f'{component_name}停用')  # PAUSE = 1

    def call_lifecycle_service(self, client, command, description):
        """调用生命周期服务。"""
        if not client.wait_for_service(timeout_sec=5.0):
            raise RuntimeError(f'{description}服务不可用')

        request = ManageLifecycleNodes.Request()
        request.command = command

        future = client.call_async(request)
        rclpy.spin_until_future_complete(self, future, timeout_sec=15.0)

        if future.result() is None:
            raise RuntimeError(f'{description}服务调用失败')

        response = future.result()
        if not response.success:
            raise RuntimeError(f'{description}失败')

        self.get_logger().info(f'{description}成功')

    def clean_tf_cache(self):
        """清理tf变换缓存。"""
        try:
            self.get_logger().info('清理TF变换缓存...')

            # 清理TF相关的临时文件
            tf_cache_commands = [
                "find /tmp -name 'tf_*' -type f -delete 2>/dev/null || true",
                "find /tmp -name 'tf2_*' -type f -delete 2>/dev/null || true",
                "find /tmp -name '*_tf_cache*' -type f -delete 2>/dev/null || true",
                "rm -rf /tmp/.tf_* 2>/dev/null || true"
            ]

            for cmd in tf_cache_commands:
                try:
                    subprocess.run(cmd, shell=True, capture_output=True, timeout=5)
                except subprocess.TimeoutExpired:
                    self.get_logger().warning(f'TF缓存清理命令超时: {cmd}')
                except Exception as e:
                    self.get_logger().warning(f'TF缓存清理命令失败 {cmd}: {str(e)}')

            # 等待一小段时间让TF系统稳定
            time.sleep(1.0)
            self.get_logger().info('TF缓存清理完成')

        except Exception as e:
            self.get_logger().error(f'TF缓存清理失败: {str(e)}')

    def run_tf_monitor(self):
        """运行TF监控。"""
        try:
            if not os.path.exists(self.tf_monitor_script):
                self.get_logger().warning(f'TF监控脚本不存在: {self.tf_monitor_script}，跳过TF监控')
                return

            self.get_logger().info('启动TF监控，等待TF转换就绪...')

            # 减少超时时间，避免长时间卡住
            result = subprocess.run(['python3', self.tf_monitor_script],
                                  capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                self.get_logger().info('TF监控完成，所有TF转换已就绪')
            else:
                self.get_logger().warning(f'TF监控退出，返回码: {result.returncode}')
                if result.stderr:
                    self.get_logger().warning(f'TF监控错误输出: {result.stderr}')
                if result.stdout:
                    self.get_logger().info(f'TF监控输出: {result.stdout}')

        except subprocess.TimeoutExpired:
            self.get_logger().warning('TF监控超时(30秒)，继续执行环境切换')
        except FileNotFoundError:
            self.get_logger().warning('TF监控脚本不存在，跳过TF监控')
        except Exception as e:
            self.get_logger().error(f'TF监控执行失败: {str(e)}，继续执行环境切换')


def main(args=None):
    """主函数。"""
    rclpy.init(args=args)

    try:
        node = MapSwitcherNode()
        rclpy.spin(node)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'地图切换节点异常: {e}')
    finally:
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
